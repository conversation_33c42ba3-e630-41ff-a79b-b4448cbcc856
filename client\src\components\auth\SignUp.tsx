import { useState, useEffect } from 'react';
import { useLocation, useSearch } from 'wouter';
import { signUpWithEmail, signInWithGoogle } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';
import Header from '@/components/Header';

export default function SignUp() {
  const [, setLocation] = useLocation();
  const search = useSearch();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isFromExtension, setIsFromExtension] = useState(false);

  // Check if the user is coming from the extension
  useEffect(() => {
    const params = new URLSearchParams(search);
    const source = params.get('source');
    const firstTime = params.get('first_time');

    if (source === 'extension' && firstTime === 'true') {
      setIsFromExtension(true);
      console.log('User is coming from the extension for the first time');
      // You could show a special welcome message or pre-fill some fields
    }
  }, [search]);

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccessMessage(null);
    console.log('Sign up form submitted');

    // Validate inputs
    if (!email || !password || !confirmPassword) {
      setError('Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    setIsLoading(true);

    try {
      console.log('Calling signUpWithEmail');
      const { data, error: signUpError } = await signUpWithEmail(email, password);

      if (signUpError) {
        console.error('Sign up error in component:', signUpError);
        throw signUpError;
      }

      console.log('Sign up response:', data);

      if (data) {
        // Different success message for extension users
        if (isFromExtension) {
          setSuccessMessage('Registration successful! Please check your email to confirm your account. You can now return to the extension and start using all features.');
        } else {
          setSuccessMessage('Registration successful! Please check your email to confirm your account.');
        }

        // Clear form
        setEmail('');
        setPassword('');
        setConfirmPassword('');

        // Redirect based on source
        setTimeout(() => {
          if (isFromExtension) {
            // For extension users, redirect to home after a longer delay
            setLocation('/');
          } else {
            // For regular users, redirect to home
            setLocation('/');
          }
        }, isFromExtension ? 5000 : 3000);
      } else {
        console.warn('Sign up response has no data');
        setError('Unable to complete registration. Please try again.');
      }
    } catch (err: any) {
      console.error('Exception in sign up handler:', err);
      setError(err.message || 'An error occurred during sign up');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setError(null);
    setIsLoading(true);
    console.log('Google sign in button clicked');

    try {
      console.log('Calling signInWithGoogle');
      const { data, error: googleError } = await signInWithGoogle();

      if (googleError) {
        console.error('Google sign in error:', googleError);
        throw googleError;
      }

      console.log('Google sign in response:', data);

      // Google auth will redirect the user to the OAuth provider
      if (data && data.url) {
        console.log('Redirecting to Google OAuth URL');
        window.location.href = data.url;
      } else {
        console.warn('No redirect URL in Google sign in response');
        setError('Unable to sign in with Google. Please try again or use email sign up.');
        setIsLoading(false);
      }
    } catch (err: any) {
      console.error('Exception in Google sign in handler:', err);
      setError(err.message || 'An error occurred during Google sign in');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen dark:bg-black light:bg-white text-foreground">
      <Header />

      <div className="container mx-auto px-4 pt-28 md:pt-32 flex justify-center items-center">
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 right-1/4 w-[30%] h-[30%] bg-primary/5 rounded-full blur-[100px] animate-pulse-slow"></div>
          <div className="absolute bottom-1/4 left-1/4 w-[20%] h-[20%] bg-secondary/5 rounded-full blur-[80px] animate-pulse-slow-reverse"></div>
        </div>

        <Card className="w-full max-w-md shadow-xl border border-muted/30 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 pointer-events-none"></div>

          <CardHeader className="space-y-2 relative z-10">
            <CardTitle className="text-3xl font-bold text-center font-inter bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">Create an account</CardTitle>
            <CardDescription className="text-center font-inter text-base">
              {isFromExtension
                ? 'Welcome from the BrowzyAI extension! Create an account to get started.'
                : 'Join browzy and supercharge your browsing experience'
              }
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-5 relative z-10">
            {isFromExtension && (
              <Alert className="bg-primary/10 text-primary border-primary/20 font-inter">
                <AlertDescription>
                  Thanks for installing the BrowzyAI extension! Create an account to unlock all features and sync your data across devices.
                </AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert variant="destructive" className="font-inter">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {successMessage && (
              <Alert className="bg-primary/20 text-primary border-primary/30 font-inter">
                <AlertDescription>{successMessage}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSignUp} className="space-y-5">
              <div className="space-y-2">
                <Label htmlFor="email" className="font-inter text-sm text-muted-foreground font-medium">EMAIL</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="h-11 bg-background/50 backdrop-blur-sm border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition font-inter"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password" className="font-inter text-sm text-muted-foreground font-medium">PASSWORD</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="h-11 bg-background/50 backdrop-blur-sm border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition font-inter"
                />
                <p className="text-xs text-muted-foreground font-inter mt-1">Password must be at least 6 characters</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="font-inter text-sm text-muted-foreground font-medium">CONFIRM PASSWORD</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className="h-11 bg-background/50 backdrop-blur-sm border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition font-inter"
                />
              </div>

              <div className="relative group/button inline-block w-full mt-2">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-secondary rounded-md blur opacity-70 group-hover/button:opacity-100 transition duration-500 group-hover/button:duration-200 animate-pulse-slow"></div>
                <Button
                  type="submit"
                  className="relative w-full h-11 text-base bg-gradient-to-r from-primary to-secondary text-white rounded-md hover:shadow-xl transition-all duration-300 transform group-hover/button:scale-[1.01] font-inter font-medium"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Please wait
                    </>
                  ) : (
                    'Create Account'
                  )}
                </Button>
              </div>
            </form>

            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-muted-foreground/20" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-card px-3 py-1 text-muted-foreground font-inter rounded-full border border-muted/30">Or continue with</span>
              </div>
            </div>

            <Button
              variant="outline"
              type="button"
              className="w-full h-11 border-muted hover:bg-background/50 hover:border-primary/30 transition-all duration-300 font-inter"
              onClick={handleGoogleSignIn}
              disabled={isLoading}
            >
              <svg className="mr-3 h-5 w-5" viewBox="0 0 24 24">
                <path
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  fill="#4285F4"
                />
                <path
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  fill="#34A853"
                />
                <path
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  fill="#FBBC05"
                />
                <path
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  fill="#EA4335"
                />
              </svg>
              Sign up with Google
            </Button>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4 relative z-10">
            <div className="text-center text-sm text-muted-foreground font-inter">
              Already have an account?{' '}
              <a
                onClick={() => setLocation('/signin')}
                className="text-primary hover:text-primary/80 cursor-pointer font-medium transition-colors duration-300"
              >
                Sign in
              </a>
            </div>
            <div className="text-center text-xs text-muted-foreground/70 font-inter">
              By creating an account, you agree to our <a href="#" className="hover:text-primary transition-colors duration-300">Terms of Service</a> and <a href="#" className="hover:text-primary transition-colors duration-300">Privacy Policy</a>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
