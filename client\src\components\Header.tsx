import { useState, useEffect } from 'react';
import { Menu, LogOut, CreditCard } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { ThemeToggle } from "@/components/ThemeToggle";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/contexts/AuthContext";
// Removed signOut import as we're using direct localStorage clearing
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const navLinks = [
  { href: "/features", label: "Features" },
  { href: "/integration", label: "Integration" },
  { href: "/contact", label: "Contact" }
];

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [, setLocation] = useLocation();
  const { user, isAuthenticated, isLoading } = useAuth();
  // Removed isSigningOut state as we're using direct sign out
  const [effectivelyAuthenticated, setEffectivelyAuthenticated] = useState(false);
  // We're using documentIsLight() instead of this state
  // const [currentTheme, setCurrentTheme] = useState(typeof window !== 'undefined' ? localStorage.getItem('theme') || 'system' : 'system');

  // Check for authentication using multiple methods
  const checkAuthentication = () => {
    // Method 1: Check context authentication
    if (isAuthenticated && user) {
      return true;
    }

    // Method 2: Check localStorage for user data
    try {
      const storedUser = localStorage.getItem('browzy_user_data');
      const userLoggedIn = localStorage.getItem('user_is_logged_in');

      if (storedUser || userLoggedIn) {
        return true;
      }

      // Method 3: Check for Supabase auth data
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.includes('supabase.auth')) {
          return true;
        }
      }

      // Method 4: Check sessionStorage
      const justLoggedIn = sessionStorage.getItem('just_logged_in');
      if (justLoggedIn === 'true') {
        return true;
      }

      // Method 5: Check URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.has('login_success')) {
        return true;
      }
    } catch (error) {
      console.error('Error checking authentication:', error);
    }

    return false;
  };

  // Update effective authentication status
  useEffect(() => {
    const isEffectivelyAuthenticated = checkAuthentication();
    setEffectivelyAuthenticated(isEffectivelyAuthenticated);

    console.log('Header auth state:', {
      user,
      isAuthenticated,
      isLoading,
      isEffectivelyAuthenticated
    });
  }, [user, isAuthenticated, isLoading]);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    // Initial check
    handleScroll();

    window.addEventListener('scroll', handleScroll);

    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // We're using documentIsLight() which checks the DOM directly,
  // so we don't need to listen for theme changes

  // Get effective user data from context or localStorage
  const getEffectiveUser = () => {
    if (user) return user;

    try {
      const storedUserData = localStorage.getItem('browzy_user_data');
      if (storedUserData) {
        return JSON.parse(storedUserData);
      }
    } catch (error) {
      console.error('Error parsing stored user data:', error);
    }

    return null;
  };

  // Get user initials for avatar fallback
  const getInitials = () => {
    const effectiveUser = getEffectiveUser();
    if (!effectiveUser?.email) return 'U';
    return effectiveUser.email.charAt(0).toUpperCase();
  };

  // Simplified sign out by directly clearing localStorage and redirecting

  // Note: We're using documentIsLight() instead of this function
  // because it's more reliable for detecting the current theme

  // Check if we're in light mode by looking at the document element
  const documentIsLight = () => {
    if (typeof document !== 'undefined') {
      return document.documentElement.classList.contains('light');
    }
    return false;
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300
      ${isScrolled
        ? documentIsLight()
          ? 'bg-white backdrop-blur-md border-b border-border/50 py-2 md:py-3 shadow-lg'
          : 'bg-black/95 backdrop-blur-md border-b border-border/50 py-2 md:py-3 shadow-lg'
        : 'bg-transparent py-3 md:py-4'}`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-10 max-w-[1400px]">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <Link href="/">
              <div className="flex items-center space-x-3 cursor-pointer">
                <div className="h-10 w-10 rounded-full flex items-center justify-center shadow-lg transform transition-transform duration-300 hover:scale-110 overflow-hidden">
                  <img src="/images/browzy-logo.png" alt="Browzy Logo" className="h-full w-full object-cover" />
                </div>
                <span className={`font-inter font-bold text-2xl ${isScrolled
                  ? (documentIsLight() ? 'text-foreground' : 'text-white')
                  : 'text-foreground'} bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary`}>
                  browzy
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center">
            <div className="mr-4 lg:mr-6 xl:mr-10 flex gap-4 lg:gap-6 xl:gap-10">
              {navLinks.map((link) => (
                link.href.startsWith('/') ? (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={`${isScrolled
                      ? (documentIsLight() ? 'text-foreground' : 'text-white')
                      : 'text-foreground'} hover:text-primary transition-all duration-300 font-medium text-base relative group transform hover:scale-105 font-inter`}
                  >
                    {link.label}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                ) : (
                  <a
                    key={link.href}
                    href={link.href}
                    className={`${isScrolled
                      ? (documentIsLight() ? 'text-foreground' : 'text-white')
                      : 'text-foreground'} hover:text-primary transition-all duration-300 font-medium text-base relative group transform hover:scale-105 font-inter`}
                  >
                    {link.label}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                  </a>
                )
              ))}
            </div>

            <div className="flex items-center gap-3 md:gap-4">
              <ThemeToggle />

              {isLoading ? (
                <Button variant="ghost" className="relative h-8 w-8 md:h-9 md:w-9 lg:h-10 lg:w-10 rounded-full">
                  <div className="w-8 h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 rounded-full bg-muted animate-pulse"></div>
                </Button>
              ) : effectivelyAuthenticated ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user?.user_metadata?.avatar_url || ''} alt={user?.email || ''} />
                        <AvatarFallback>{getInitials()}</AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{user?.email?.split('@')[0]}</p>
                        <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => {
                        console.log('Sign out clicked');
                        localStorage.clear();
                        window.location.href = '/';
                      }}
                      className="font-inter text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20"
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Sign Out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <>
                  <Button
                    variant="outline"
                    onClick={() => setLocation('/signin')}
                    className="px-4 md:px-5 lg:px-6 py-2 text-sm md:text-base border-2 border-primary text-primary hover:bg-primary/10 rounded-lg transition-all duration-300 transform hover:scale-105 font-inter"
                  >
                    Sign In
                  </Button>
                  <div className="relative group">
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-secondary rounded-lg blur opacity-50 group-hover:opacity-100 transition duration-300"></div>
                    <Button
                      onClick={() => setLocation('/signup')}
                      className="relative px-4 md:px-5 lg:px-6 py-2 text-sm md:text-base bg-gradient-to-r from-primary to-secondary text-white hover:opacity-90 rounded-lg shadow-md transition-all duration-300 transform hover:scale-105 font-inter"
                    >
                      Sign Up
                    </Button>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Mobile Menu */}
          <div className="md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className={`${isScrolled
                  ? (documentIsLight() ? 'text-foreground' : 'text-white')
                  : 'text-foreground'} hover:text-primary transition`}>
                  <Menu size={24} />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="bg-background/95 backdrop-blur-md border-l border-border/50">
                <div className="flex flex-col space-y-6 mt-10">
                  {navLinks.map((link) => (
                    link.href.startsWith('/') ? (
                      <Link
                        key={link.href}
                        href={link.href}
                        className="text-foreground hover:text-primary transition font-medium py-3 px-4 border-b border-border/30 font-inter"
                      >
                        {link.label}
                      </Link>
                    ) : (
                      <a
                        key={link.href}
                        href={link.href}
                        className="text-foreground hover:text-primary transition font-medium py-3 px-4 border-b border-border/30 font-inter"
                      >
                        {link.label}
                      </a>
                    )
                  ))}
                  <div className="flex items-center justify-between py-3 px-4 border-b border-border/30">
                    <span className="font-medium font-inter">Theme</span>
                    <ThemeToggle />
                  </div>
                  <div className="flex flex-col gap-4 pt-6 px-4">
                    {isLoading ? (
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="w-10 h-10 rounded-full bg-muted animate-pulse"></div>
                        <div className="flex flex-col space-y-2">
                          <div className="h-4 w-24 bg-muted animate-pulse rounded"></div>
                          <div className="h-3 w-32 bg-muted animate-pulse rounded"></div>
                        </div>
                      </div>
                    ) : effectivelyAuthenticated ? (
                      <>
                        <div className="flex items-center space-x-3 mb-2">
                          <Avatar className="h-10 w-10">
                            <AvatarImage
                              src={getEffectiveUser()?.user_metadata?.avatar_url || ''}
                              alt={getEffectiveUser()?.email || 'User'}
                            />
                            <AvatarFallback>{getInitials()}</AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col">
                            <span className="text-sm font-medium font-inter">Account</span>
                            <span className="text-xs text-muted-foreground truncate max-w-[180px] font-inter">
                              {getEffectiveUser()?.email || 'User'}
                            </span>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          onClick={() => {
                            console.log('Sign out clicked (mobile)');
                            localStorage.clear();
                            window.location.href = '/';
                          }}
                          className="w-full font-inter border-red-500/50 text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-950/20"
                        >
                          <LogOut className="mr-2 h-4 w-4" />
                          Sign Out
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="outline"
                          onClick={() => setLocation('/signin')}
                          className="w-full py-3 text-base border-2 border-primary text-primary hover:bg-primary/10 rounded-lg transition-all duration-300 font-inter"
                        >
                          Sign In
                        </Button>
                        <div className="relative group w-full">
                          <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-secondary rounded-lg blur opacity-50 group-hover:opacity-100 transition duration-300"></div>
                          <Button
                            onClick={() => setLocation('/signup')}
                            className="relative w-full py-3 text-base bg-gradient-to-r from-primary to-secondary text-white hover:opacity-90 rounded-lg shadow-md transition-all duration-300 font-inter"
                          >
                            Sign Up
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
}
