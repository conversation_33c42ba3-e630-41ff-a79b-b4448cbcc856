// Base URL for API requests
const API_BASE_URL = '/api';

/**
 * Helper function to make authenticated API requests
 * @param url API endpoint
 * @param options Fetch options
 * @returns Response data
 */
async function fetchWithAuth(url: string, options: RequestInit = {}) {
  try {
    // Get the token from localStorage
    const token = localStorage.getItem('auth_token');

    if (!token) {
      throw new Error('No authentication token available');
    }

    // Add authorization header
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers,
    };

    // Make the request
    const response = await fetch(`${API_BASE_URL}${url}`, {
      ...options,
      headers,
      credentials: 'include', // Include cookies
    });

    // Parse the response
    const responseData = await response.json();

    // Check if the request was successful
    if (!response.ok) {
      throw new Error(responseData.message || 'API request failed');
    }

    return responseData;
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
}

/**
 * Sign in with email and password
 * @param email User's email
 * @param password User's password
 * @returns Authentication result
 */
export async function signIn(email: string, password: string) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
      credentials: 'include', // Include cookies
    });

    const responseData = await response.json();

    if (response.ok && responseData.session) {
      // Store token in localStorage for client-side access
      localStorage.setItem('auth_token', responseData.session.access_token);
    }

    return {
      success: response.ok,
      message: responseData.message,
      user: responseData.user,
      session: responseData.session,
      error: responseData.error,
    };
  } catch (error: any) {
    console.error('Sign in error:', error);
    return {
      success: false,
      message: error.message || 'Error during sign in',
      user: null,
      session: null,
      error: error.message,
    };
  }
}

/**
 * Sign up with email and password
 * @param email User's email
 * @param password User's password
 * @returns Registration result
 */
export async function signUp(email: string, password: string) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
      credentials: 'include', // Include cookies
    });

    const responseData = await response.json();

    return {
      success: response.ok,
      message: responseData.message,
      user: responseData.user,
      session: responseData.session,
      error: responseData.error,
    };
  } catch (error: any) {
    console.error('Sign up error:', error);
    return {
      success: false,
      message: error.message || 'Error during sign up',
      user: null,
      session: null,
      error: error.message,
    };
  }
}

/**
 * Sign in with Google
 * @returns Google authentication URL
 */
export async function signInWithGoogle() {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/google`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies
    });

    const responseData = await response.json();

    return {
      success: response.ok,
      message: responseData.message,
      url: responseData.url,
      error: responseData.error,
    };
  } catch (error: any) {
    console.error('Google sign in error:', error);
    return {
      success: false,
      message: error.message || 'Error during Google sign in',
      url: null,
      error: error.message,
    };
  }
}

/**
 * Sign out the current user
 * @returns Sign out result
 */
export async function signOut() {
  try {
    const token = localStorage.getItem('auth_token');

    if (!token) {
      return { success: false, message: 'No authentication token available' };
    }

    const response = await fetch(`${API_BASE_URL}/auth/signout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token }),
      credentials: 'include', // Include cookies
    });

    const responseData = await response.json();

    // Clear token from localStorage
    localStorage.removeItem('auth_token');

    return {
      success: response.ok,
      message: responseData.message,
      error: responseData.error,
    };
  } catch (error: any) {
    console.error('Sign out error:', error);

    // Clear token from localStorage even if the request fails
    localStorage.removeItem('auth_token');

    return {
      success: false,
      message: error.message || 'Error during sign out',
      error: error.message,
    };
  }
}

/**
 * Refresh the current session
 * @param refreshToken Refresh token
 * @returns Refresh result
 */
export async function refreshSession(refreshToken: string) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
      credentials: 'include', // Include cookies
    });

    const responseData = await response.json();

    if (response.ok && responseData.session) {
      // Store token in localStorage for client-side access
      localStorage.setItem('auth_token', responseData.session.access_token);
    }

    return {
      success: response.ok,
      message: responseData.message,
      session: responseData.session,
      error: responseData.error,
    };
  } catch (error: any) {
    console.error('Session refresh error:', error);
    return {
      success: false,
      message: error.message || 'Error during session refresh',
      session: null,
      error: error.message,
    };
  }
}

/**
 * Verify the current user's token with the backend
 * @returns Verification result
 */
export async function verifyToken() {
  try {
    const token = localStorage.getItem('auth_token');

    if (!token) {
      return { valid: false, message: 'No token available' };
    }

    // Make the request to verify the token
    const response = await fetch(`${API_BASE_URL}/auth/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token }),
      credentials: 'include', // Include cookies
    });

    const responseData = await response.json();

    return {
      valid: response.ok,
      message: responseData.message,
      user: responseData.user,
    };
  } catch (error: any) {
    console.error('Token verification error:', error);
    return {
      valid: false,
      message: error.message || 'Error verifying token'
    };
  }
}

/**
 * Get the current user's profile
 * @returns User profile data
 */
export async function getUserProfile() {
  return fetchWithAuth('/user/profile');
}

/**
 * Update user preferences
 * @param preferences User preferences object
 * @returns Updated profile data
 */
export async function updateUserPreferences(preferences: any) {
  return fetchWithAuth('/user/preferences', {
    method: 'PUT',
    body: JSON.stringify({ preferences }),
  });
}

/**
 * Submit contact form
 * @param formData Contact form data
 * @returns Submission result
 */
export async function submitContactForm(formData: {
  name: string;
  email: string;
  subject: string;
  message: string;
  consent: boolean;
}) {
  try {
    const response = await fetch(`${API_BASE_URL}/contact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData),
      credentials: 'include', // Include cookies
    });

    const responseData = await response.json();

    return {
      success: response.ok,
      message: responseData.message,
      status: responseData.status,
    };
  } catch (error) {
    console.error('Contact form submission error:', error);
    return {
      success: false,
      message: 'Error submitting contact form',
      status: 'error',
    };
  }
}
