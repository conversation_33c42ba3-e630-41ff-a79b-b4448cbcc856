import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },  build: {
    outDir: "../dist/public",
    emptyOutDir: true,
    rollupOptions: {
      external: ['react-router-dom', 'qrcode.react'],
    },
  },
  server: {
    hmr: {
      overlay: false,
    },
    host: "0.0.0.0",
  },
});
