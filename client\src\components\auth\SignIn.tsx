import { useState, useEffect } from 'react';
import { useLocation, useSearch } from 'wouter';
import { signInWithEmail, signInWithGoogle } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';
import Header from '@/components/Header';
import { useAuth } from '@/contexts/AuthContext';
import { Checkbox } from '@/components/ui/checkbox';

export default function SignIn() {
  const [, setLocation] = useLocation();
  const search = useSearch();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(true);
  const [isFromExtension, setIsFromExtension] = useState(false);
  const { saveLoginCredentials } = useAuth();

  // Check if the user is coming from the extension
  useEffect(() => {
    const params = new URLSearchParams(search);
    const source = params.get('source');

    if (source === 'extension') {
      setIsFromExtension(true);
      console.log('User is coming from the extension');
    }
  }, [search]);

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    console.log('Sign in form submitted');

    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setIsLoading(true);

    try {
      console.log('Calling signInWithEmail');
      const { data, error: signInError } = await signInWithEmail(email, password);

      if (signInError) {
        console.error('Sign in error in component:', signInError);
        throw signInError;
      }

      if (data && data.user) {
        console.log('Sign in successful, redirecting to dashboard');

        // Always save user data to localStorage for persistence
        try {
          localStorage.setItem('browzy_user_data', JSON.stringify(data.user));

          if (data.session) {
            localStorage.setItem('browzy_session_data', JSON.stringify(data.session));
            localStorage.setItem('auth_token', data.session.access_token);
          }

          // Set a flag that user is logged in
          localStorage.setItem('user_is_logged_in', 'true');

          console.log('Saved authentication data to localStorage');

          // If "Remember me" is checked, save credentials for auto-login
          if (rememberMe) {
            console.log('Saving login credentials for auto-login');
            saveLoginCredentials(email, password);
          }
        } catch (storageError) {
          console.error('Error saving auth data to localStorage:', storageError);
        }

        // Store a login success flag in sessionStorage
        // This is more reliable than cookies or localStorage for immediate use
        try {
          sessionStorage.setItem('just_logged_in', 'true');
          sessionStorage.setItem('login_timestamp', new Date().toISOString());

          // Also store in localStorage for longer persistence
          localStorage.setItem('user_is_logged_in', 'true');
          localStorage.setItem('login_timestamp', new Date().toISOString());

          console.log('Stored login success flags in sessionStorage and localStorage');
        } catch (storageError) {
          console.error('Error storing login flags:', storageError);
        }

        // Use direct navigation with a timestamp to prevent caching
        const timestamp = Date.now();
        const extensionParam = isFromExtension ? '&extension=true' : '';
        sessionStorage.setItem('just_logged_in', 'true');
        // Redirect to home instead of dashboard
        window.location.href = `/?login_success=true&t=${timestamp}${extensionParam}`;
      } else {
        console.warn('Sign in response has no user data:', data);
        setError('Unable to sign in. Please try again.');
      }
    } catch (err: any) {
      console.error('Exception in sign in handler:', err);
      setError(err.message || 'An error occurred during sign in');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setError(null);
    setIsLoading(true);
    console.log('Google sign in button clicked');

    try {
      console.log('Calling signInWithGoogle');
      const { data, error: googleError } = await signInWithGoogle();

      if (googleError) {
        console.error('Google sign in error:', googleError);
        throw googleError;
      }

      console.log('Google sign in response:', data);

      // Google auth will redirect the user to the OAuth provider
      if (data && data.url) {
        console.log('Redirecting to Google OAuth URL');
        window.location.href = data.url;
      } else {
        console.warn('No redirect URL in Google sign in response');
        setError('Unable to sign in with Google. Please try again or use email sign in.');
        setIsLoading(false);
      }
    } catch (err: any) {
      console.error('Exception in Google sign in handler:', err);
      setError(err.message || 'An error occurred during Google sign in');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen dark:bg-black light:bg-white text-foreground">
      <Header />

      <div className="container mx-auto px-4 pt-28 md:pt-32 flex justify-center items-center">
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-[30%] h-[30%] bg-primary/5 rounded-full blur-[100px] animate-pulse-slow"></div>
          <div className="absolute bottom-1/4 right-1/4 w-[20%] h-[20%] bg-secondary/5 rounded-full blur-[80px] animate-pulse-slow-reverse"></div>
        </div>

        <Card className="w-full max-w-md shadow-xl border border-muted/30 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 pointer-events-none"></div>

          <CardHeader className="space-y-2 relative z-10">
            <CardTitle className="text-3xl font-bold text-center font-inter bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">Sign in to browzy</CardTitle>
            <CardDescription className="text-center font-inter text-base">
              {isFromExtension
                ? 'Sign in to connect your BrowzyAI extension'
                : 'Enter your email and password to sign in'
              }
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-5 relative z-10">
            {isFromExtension && (
              <Alert className="bg-primary/10 text-primary border-primary/20 font-inter">
                <AlertDescription>
                  Sign in to connect your BrowzyAI extension and access all premium features.
                </AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert variant="destructive" className="font-inter">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSignIn} className="space-y-5">
              <div className="space-y-2">
                <Label htmlFor="email" className="font-inter text-sm text-muted-foreground font-medium">EMAIL</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="h-11 bg-background/50 backdrop-blur-sm border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition font-inter"
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password" className="font-inter text-sm text-muted-foreground font-medium">PASSWORD</Label>
                  <a
                    className="text-sm text-primary underline-offset-4 hover:underline cursor-pointer font-inter"
                    onClick={() => setLocation('/forgot-password')}
                  >
                    Forgot password?
                  </a>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="h-11 bg-background/50 backdrop-blur-sm border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition font-inter"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                  className="border-muted data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />
                <Label htmlFor="remember" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 font-inter">
                  Remember me for 30 days
                </Label>
              </div>

              <div className="relative group/button inline-block w-full">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-secondary rounded-md blur opacity-70 group-hover/button:opacity-100 transition duration-500 group-hover/button:duration-200 animate-pulse-slow"></div>
                <Button
                  type="submit"
                  className="relative w-full h-11 text-base bg-gradient-to-r from-primary to-secondary text-white rounded-md hover:shadow-xl transition-all duration-300 transform group-hover/button:scale-[1.01] font-inter font-medium"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Please wait
                    </>
                  ) : (
                    'Sign In'
                  )}
                </Button>
              </div>
            </form>

            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-muted-foreground/20" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-card px-3 py-1 text-muted-foreground font-inter rounded-full border border-muted/30">Or continue with</span>
              </div>
            </div>

            <Button
              variant="outline"
              type="button"
              className="w-full h-11 border-muted hover:bg-background/50 hover:border-primary/30 transition-all duration-300 font-inter"
              onClick={handleGoogleSignIn}
              disabled={isLoading}
            >
              <svg className="mr-3 h-5 w-5" viewBox="0 0 24 24">
                <path
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  fill="#4285F4"
                />
                <path
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  fill="#34A853"
                />
                <path
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  fill="#FBBC05"
                />
                <path
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  fill="#EA4335"
                />
              </svg>
              Sign in with Google
            </Button>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4 relative z-10">
            <div className="text-center text-sm text-muted-foreground font-inter">
              Don't have an account?{' '}
              <a
                onClick={() => setLocation('/signup')}
                className="text-primary hover:text-primary/80 cursor-pointer font-medium transition-colors duration-300"
              >
                Create an account
              </a>
            </div>
            <div className="text-center text-xs text-muted-foreground/70 font-inter">
              By signing in, you agree to our <a href="#" className="hover:text-primary transition-colors duration-300">Terms of Service</a> and <a href="#" className="hover:text-primary transition-colors duration-300">Privacy Policy</a>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
