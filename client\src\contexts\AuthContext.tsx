import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase, getCurrentUser, getSession } from '@/lib/supabase';
import { verifyToken } from '@/lib/api';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  refreshUser: () => Promise<void>;
  saveLoginCredentials: (email: string, password: string) => void;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isLoading: true,
  isAuthenticated: false,
  refreshUser: async () => {},
  saveLoginCredentials: () => {},
});

export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: ReactNode;
}

// Storage keys for persistent login
const AUTH_CREDENTIALS_KEY = 'browzy_auth_credentials';
const USER_DATA_KEY = 'browzy_user_data';
const SESSION_DATA_KEY = 'browzy_session_data';

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Function to save login session info
  const saveLoginCredentials = (email: string, _password: string) => {
    try {
      // SECURITY NOTE: We intentionally don't store the password
      // In a production app, you should use a token-based approach instead
      // This is a simplified implementation for development purposes only

      // Store only the email and expiration, not the password
      const credentials = {
        email,
        // Flag to indicate we have credentials stored
        hasStoredCredentials: true,
        savedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      };

      // Save to localStorage
      localStorage.setItem(AUTH_CREDENTIALS_KEY, JSON.stringify(credentials));
      console.log('Login session info saved');
    } catch (error) {
      console.error('Error saving login session info:', error);
    }
  };

  // Function to refresh user data
  const refreshUser = async () => {
    try {
      console.log('Refreshing user data');
      const { data } = await getCurrentUser();
      if (data?.user) {
        setUser(data.user);
        // Save user data to localStorage
        localStorage.setItem(USER_DATA_KEY, JSON.stringify(data.user));
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  };

  // Initialize auth state
  useEffect(() => {
    // Set a timeout to ensure loading state doesn't get stuck
    const loadingTimeout = setTimeout(() => {
      if (isLoading) {
        console.log('Loading timeout reached, forcing loading state to false');
        setIsLoading(false);
      }
    }, 5000); // 5 seconds timeout

    // More robust cookie checking function
    const getCookieValue = (name: string): string | null => {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.startsWith(name + '=')) {
          return cookie.substring(name.length + 1);
        }
      }
      return null;
    };

    // Check for any auth-related cookies
    const checkForAuthCookies = () => {
      console.log('Checking for auth cookies...');
      console.log('All cookies:', document.cookie);

      const userLoggedIn = getCookieValue('user-logged-in');
      const authToken = getCookieValue('supabase-auth-token');
      const refreshToken = getCookieValue('supabase-auth-refresh');
      const userSession = getCookieValue('user-session');
      const rememberedUser = getCookieValue('remembered_user');

      console.log('user-logged-in cookie:', userLoggedIn);
      console.log('auth token cookie exists:', !!authToken);
      console.log('refresh token cookie exists:', !!refreshToken);
      console.log('user-session cookie exists:', !!userSession);
      console.log('remembered_user cookie:', rememberedUser);

      return {
        isLoggedIn: !!userLoggedIn,
        hasAuthToken: !!authToken,
        hasRefreshToken: !!refreshToken,
        hasUserSession: !!userSession,
        isRemembered: !!rememberedUser
      };
    };

    // Robust session refresh function
    const refreshSessionIfNeeded = async () => {
      const authCookies = checkForAuthCookies();

      // If we have any indication of a logged-in user but no active session
      if ((authCookies.isLoggedIn || authCookies.hasRefreshToken || authCookies.isRemembered) && !session) {
        try {
          console.log('Attempting to restore session from cookies');

          // First try to get the session directly from Supabase
          const { data: sessionData } = await supabase.auth.getSession();

          if (sessionData?.session) {
            console.log('Session retrieved from Supabase:', sessionData.session);
            setSession(sessionData.session);

            // Get user data
            const { data: userData } = await getCurrentUser();
            if (userData?.user) {
              console.log('User data retrieved:', userData.user);
              setUser(userData.user);
              return true;
            }
          }

          // If that fails, try to refresh the session using the refresh token
          if (authCookies.hasRefreshToken) {
            console.log('Attempting to refresh session with refresh token');

            // Call the server endpoint to refresh the session
            const response = await fetch('/api/auth/refresh', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include', // Important for cookies
            });

            if (response.ok) {
              const refreshData = await response.json();
              console.log('Session refreshed successfully:', refreshData);

              // Force reload the page to apply the new session
              window.location.reload();
              return true;
            } else {
              console.error('Failed to refresh session');
            }
          }

          // If we still don't have a session, clear all cookies and localStorage
          if (!session) {
            console.log('Failed to restore session, clearing auth state');
            clearAuthState();
          }
        } catch (error) {
          console.error('Error refreshing session:', error);
        }
      }

      return false;
    };

    // Function to clear all auth state
    const clearAuthState = () => {
      console.log('Clearing all auth state');

      // Clear user and session state
      setUser(null);
      setSession(null);

      // Clear all auth-related cookies
      document.cookie = 'user-logged-in=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      document.cookie = 'remembered_user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      document.cookie = 'oauth_signin=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      document.cookie = 'user-session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

      // Clear localStorage items
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('supabase') || key.includes('auth'))) {
          localStorage.removeItem(key);
        }
      }
    };

    // Call refresh session on mount
    refreshSessionIfNeeded();

    // Set up session refresh interval (every hour)
    const refreshInterval = setInterval(async () => {
      try {
        const { data } = await getSession();
        if (data?.session) {
          console.log('Refreshing session to maintain persistence');
          await supabase.auth.refreshSession();
        } else {
          // If we don't have a session but have auth cookies, try to refresh
          refreshSessionIfNeeded();
        }
      } catch (error) {
        console.error('Error in refresh interval:', error);
      }
    }, 60 * 60 * 1000); // 1 hour

    const initAuth = async () => {
      try {
        console.log('Initializing auth state');
        setIsLoading(true);

        // First, try to get session from Supabase
        const { data: sessionData } = await getSession();
        console.log('Session data from Supabase:', sessionData);

        if (sessionData?.session) {
          console.log('Found active session, restoring auth state');
          setSession(sessionData.session);

          // Save session to localStorage for persistence
          localStorage.setItem(SESSION_DATA_KEY, JSON.stringify(sessionData.session));

          // Get user data
          const { data: userData } = await getCurrentUser();
          console.log('User data:', userData);

          if (userData?.user) {
            setUser(userData.user);
            // Save user to localStorage
            localStorage.setItem(USER_DATA_KEY, JSON.stringify(userData.user));
            return; // Successfully restored session
          }
        }

        // If we don't have a session, try to restore from localStorage
        console.log('No active session, checking localStorage');
        const storedUser = localStorage.getItem(USER_DATA_KEY);
        const storedSession = localStorage.getItem(SESSION_DATA_KEY);

        if (storedUser && storedSession) {
          console.log('Found stored user and session data, attempting to restore');
          try {
            const parsedUser = JSON.parse(storedUser);
            const parsedSession = JSON.parse(storedSession);

            // Verify the session is still valid
            const { valid } = await verifyToken();

            if (valid) {
              console.log('Stored session is valid, restoring auth state');
              setUser(parsedUser);
              setSession(parsedSession);
              return; // Successfully restored from localStorage
            } else {
              console.log('Stored session is invalid, attempting auto-login');
            }
          } catch (parseError) {
            console.error('Error parsing stored user/session:', parseError);
          }
        }

        // If we still don't have a session, check for stored session info
        console.log('Checking for stored session info');
        const storedCredentials = localStorage.getItem(AUTH_CREDENTIALS_KEY);

        if (storedCredentials) {
          try {
            const credentials = JSON.parse(storedCredentials);
            const now = new Date();
            const expiresAt = new Date(credentials.expiresAt);

            // Check if session info is still valid (not expired)
            if (now < expiresAt && credentials.email && credentials.hasStoredCredentials) {
              console.log('Found valid stored session, but auto-login requires authentication');
              console.log('Redirecting to login page...');

              // Instead of auto-login with stored password (security risk),
              // we'll just inform the user they need to log in again
              // You could redirect to login page with the email pre-filled

              // We're not actually attempting auto-login anymore for security reasons
              console.log('Please sign in again to continue');

              // You could store the email to pre-fill the login form
              sessionStorage.setItem('lastLoginEmail', credentials.email);
            } else {
              console.log('Stored credentials expired, removing');
              localStorage.removeItem(AUTH_CREDENTIALS_KEY);
            }
          } catch (credError) {
            console.error('Error parsing stored credentials:', credError);
            localStorage.removeItem(AUTH_CREDENTIALS_KEY);
          }
        }

        // If we get here, we couldn't restore the session
        console.log('Could not restore session, user needs to log in');
        setUser(null);
        setSession(null);

      } catch (error) {
        console.error('Error initializing auth:', error);
        // Reset state on error
        setUser(null);
        setSession(null);
      } finally {
        // Always set loading to false when done
        setIsLoading(false);
      }
    };

    // Initialize auth state
    initAuth();

    // Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        console.log('Auth state changed:', event, newSession);

        // Set loading to true during auth state change
        setIsLoading(true);

        try {
          // Handle sign out event specifically
          if (event === 'SIGNED_OUT') {
            console.log('User signed out, clearing auth state');
            setUser(null);
            setSession(null);

            // Clear all Supabase-related items from localStorage
            try {
              console.log('Clearing Supabase items from localStorage');
              for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.includes('supabase')) {
                  console.log('Removing localStorage item:', key);
                  localStorage.removeItem(key);
                }
              }
            } catch (e) {
              console.error('Error clearing localStorage:', e);
            }

            // No need to fetch user data when signed out
            setIsLoading(false);
            return;
          }

          setSession(newSession);

          if (newSession) {
            // Get user data
            const { data } = await getCurrentUser();
            console.log('User data from auth change:', data);

            if (data?.user) {
              setUser(data.user);
            }
          } else {
            // No session, ensure user is null
            setUser(null);
          }
        } catch (error) {
          console.error('Error in auth state change:', error);
          // Reset state on error
          setUser(null);
          setSession(null);
        } finally {
          // Always set loading to false when done
          setIsLoading(false);
        }
      }
    );

    // Clean up subscription, timeout, and interval
    return () => {
      authListener.subscription.unsubscribe();
      clearTimeout(loadingTimeout);
      clearInterval(refreshInterval);
    };
  }, []);

  const value = {
    user,
    session,
    isLoading,
    isAuthenticated: !!user,
    refreshUser,
    saveLoginCredentials,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
