import { ReactNode, useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const [, setLocation] = useLocation();
  const [authChecked, setAuthChecked] = useState(false);

  // Check for authentication using multiple methods
  const checkAuthentication = () => {
    try {
      console.log('Checking authentication status in ProtectedRoute');

      // Method 1: Check URL parameters for login success
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.has('login_success')) {
        console.log('Found login_success parameter in URL');
        return true;
      }

      // Method 2: Check sessionStorage for just_logged_in flag
      try {
        const justLoggedIn = sessionStorage.getItem('just_logged_in');
        if (justLoggedIn === 'true') {
          console.log('Found just_logged_in flag in sessionStorage');
          return true;
        }
      } catch (e) {
        console.error('Error checking sessionStorage:', e);
      }

      // Method 3: Check localStorage for our custom auth data
      const storedUser = localStorage.getItem('browzy_user_data');
      const storedSession = localStorage.getItem('browzy_session_data');
      const userLoggedIn = localStorage.getItem('user_is_logged_in');

      if (storedUser && (storedSession || userLoggedIn)) {
        console.log('Found user data in localStorage, considering authenticated');
        return true;
      }

      // Method 4: Check for auth token
      const authToken = localStorage.getItem('auth_token');
      if (authToken) {
        console.log('Found auth token in localStorage, considering authenticated');
        return true;
      }

      // Method 5: Check for Supabase auth data
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.includes('supabase.auth')) {
          console.log('Found Supabase auth data in localStorage, considering authenticated');
          return true;
        }
      }

      // Method 6: Check cookies
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.startsWith('user-logged-in=') ||
            cookie.startsWith('supabase-auth-token=') ||
            cookie.startsWith('user-session=')) {
          console.log('Found authentication cookie:', cookie);
          return true;
        }
      }

      console.log('No authentication data found by any method');
      return false;
    } catch (error) {
      console.error('Error checking authentication:', error);
      return false;
    }
  };

  useEffect(() => {
    // Only redirect if authentication check is complete (not loading) and user is not authenticated
    if (!isLoading) {
      const isLocallyAuthenticated = checkAuthentication();

      if (!isAuthenticated && !isLocallyAuthenticated) {
        console.log('User not authenticated, redirecting to sign in from protected route');
        // Use direct navigation to avoid routing issues
        window.location.href = '/signin';
      }

      setAuthChecked(true);
    }
  }, [isAuthenticated, isLoading, setLocation]);

  // Show loading state while checking authentication
  if (isLoading || !authChecked) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Check both context auth state and local authentication
  const isLocallyAuthenticated = checkAuthentication();

  // Render children if authenticated by any means
  return (isAuthenticated || isLocallyAuthenticated) ? <>{children}</> : null;
}
