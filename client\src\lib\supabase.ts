import { createClient } from '@supabase/supabase-js';

// Initialize the Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

// Debug environment variables
console.log('Supabase URL:', supabaseUrl ? 'Set correctly' : 'Missing');
console.log('Supabase Anon Key:', supabaseAnonKey ? 'Set correctly' : 'Missing');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase URL or Anon Key. Please check your environment variables.');
}

// Create the Supabase client with additional options
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storageKey: 'supabase.auth.token',
    // Use implicit flow type for longer sessions
    flowType: 'implicit',
    storage: {
      getItem: (key) => {
        try {
          console.log(`Trying to get auth item: ${key}`);

          // Try to get from cookies first (preferred for persistence)
          const cookies = document.cookie.split(';');
          for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith(key + '=')) {
              const value = cookie.substring(key.length + 1);
              console.log(`Found ${key} in cookies`);
              return value;
            }
          }

          // Fall back to localStorage
          const storedItem = localStorage.getItem(key);
          if (storedItem) {
            console.log(`Found ${key} in localStorage`);
            return storedItem;
          }

          console.log(`${key} not found in cookies or localStorage`);
          return null;
        } catch (error) {
          console.error('Error getting auth item:', error);
          return null;
        }
      },
      setItem: (key, value) => {
        try {
          console.log(`Setting auth item: ${key}`);

          // Set in localStorage for compatibility
          localStorage.setItem(key, value);

          // Also set in cookies with 30-day expiration
          const expirationDate = new Date();
          expirationDate.setDate(expirationDate.getDate() + 30); // 30 days

          // Set the cookie with proper attributes for better persistence
          document.cookie = `${key}=${value}; expires=${expirationDate.toUTCString()}; path=/; SameSite=Lax; max-age=${30*24*60*60}`;

          console.log(`Set ${key} in both localStorage and cookies with 30-day expiration`);

          // Also set a user-logged-in flag cookie that can be read by the frontend
          if (key.includes('supabase.auth.token')) {
            document.cookie = `user-logged-in=true; expires=${expirationDate.toUTCString()}; path=/; SameSite=Lax; max-age=${30*24*60*60}`;
            console.log('Set user-logged-in cookie');
          }

          return;
        } catch (error) {
          console.error('Error setting auth item:', error);
        }
      },
      removeItem: (key) => {
        try {
          console.log(`Removing auth item: ${key}`);

          // Remove from localStorage
          localStorage.removeItem(key);

          // Remove from cookies by setting expiration in the past
          document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax; max-age=0`;

          console.log(`Removed ${key} from localStorage and cookies`);

          // Also clear the user-logged-in flag if we're removing auth tokens
          if (key.includes('supabase.auth.token')) {
            document.cookie = `user-logged-in=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax; max-age=0`;
            console.log('Cleared user-logged-in cookie');
          }

          return;
        } catch (error) {
          console.error('Error removing auth item:', error);
        }
      }
    }
  }
});

// Auth helper functions
export const signUpWithEmail = async (email: string, password: string) => {
  console.log('Signing up with email:', email);

  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}`,
      }
    });

    if (error) {
      console.error('Sign up error:', error);
    } else {
      console.log('Sign up successful:', data);
    }

    return { data, error };
  } catch (e) {
    console.error('Exception during sign up:', e);
    return { data: null, error: e as Error };
  }
};

export const signInWithEmail = async (email: string, password: string) => {
  console.log('Signing in with email:', email);

  try {
    // Sign in with password and set session persistence
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error('Sign in error:', error);
    } else {
      console.log('Sign in successful:', data);

      // Set a cookie to remember the user for 30 days
      try {
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 30); // 30 days

        // Store a simple flag that the user is remembered
        document.cookie = `remembered_user=true; expires=${expirationDate.toUTCString()}; path=/; SameSite=Strict`;

        console.log('Set remembered_user cookie for 30 days');
      } catch (cookieError) {
        console.error('Error setting remember cookie:', cookieError);
      }
    }

    return { data, error };
  } catch (e) {
    console.error('Exception during sign in:', e);
    return { data: null, error: e as Error };
  }
};

export const signInWithGoogle = async () => {
  console.log('Signing in with Google');

  try {
    // Set a cookie to remember the user for 30 days after OAuth redirect
    // This needs to be done before the redirect since we can't set it after
    try {
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 30); // 30 days

      // Store a flag that we're in the process of OAuth sign-in
      document.cookie = `oauth_signin=true; expires=${expirationDate.toUTCString()}; path=/; SameSite=Strict`;

      console.log('Set oauth_signin cookie for 30 days');
    } catch (cookieError) {
      console.error('Error setting OAuth cookie:', cookieError);
    }

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}`,
        queryParams: {
          prompt: 'select_account',
        }
      },
    });

    if (error) {
      console.error('Google sign in error:', error);
    } else {
      console.log('Google sign in successful, redirect URL:', data?.url);
    }

    return { data, error };
  } catch (e) {
    console.error('Exception during Google sign in:', e);
    return { data: null, error: e as Error };
  }
};

export const signOut = async () => {
  console.log('Attempting to sign out');

  try {
    // Clear all Supabase-related items from localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.includes('supabase')) {
        console.log('Removing localStorage item:', key);
        localStorage.removeItem(key);
      }
    }

    // Clear all cookies related to authentication
    console.log('Clearing authentication cookies');
    const cookiesToClear = ['remembered_user', 'oauth_signin', 'supabase.auth.token'];
    cookiesToClear.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict`;
    });

    // Sign out from Supabase
    console.log('Calling supabase.auth.signOut()');
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('Sign out error:', error);
      return { error, success: false };
    }

    console.log('Sign out API call successful');

    // Force clear the auth state
    try {
      console.log('Forcing session clear');
      await supabase.auth.setSession({
        access_token: '',
        refresh_token: ''
      });
    } catch (sessionError) {
      console.error('Error clearing session:', sessionError);
      // Continue even if this fails
    }

    // Double-check if we're still authenticated
    const { data: sessionData } = await supabase.auth.getSession();
    if (sessionData?.session) {
      console.warn('Session still exists after sign out, forcing manual cleanup');
      // If we still have a session, something went wrong
      // Force a manual cleanup
      localStorage.clear();
    } else {
      console.log('Session successfully cleared');
    }

    return { error: null, success: true };
  } catch (e) {
    console.error('Exception during sign out:', e);

    // Even if there's an error, try to clear localStorage as a fallback
    try {
      localStorage.clear();
      console.log('Cleared localStorage as fallback');
    } catch (storageError) {
      console.error('Failed to clear localStorage:', storageError);
    }

    return { error: e as Error, success: false };
  }
};

export const getCurrentUser = async () => {
  try {
    const { data, error } = await supabase.auth.getUser();

    if (error) {
      console.error('Get user error:', error);
    } else {
      console.log('Get user successful:', data);
    }

    return { data, error };
  } catch (e) {
    console.error('Exception during get user:', e);
    return { data: null, error: e as Error };
  }
};

export const getSession = async () => {
  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Get session error:', error);
    } else {
      console.log('Get session successful:', data);
    }

    return { data, error };
  } catch (e) {
    console.error('Exception during get session:', e);
    return { data: null, error: e as Error };
  }
};
